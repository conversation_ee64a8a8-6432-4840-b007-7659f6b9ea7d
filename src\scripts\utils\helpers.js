/**
 * 通用工具函数
 * 提供各种实用的辅助功能
 */

/**
 * 获取平台名称映射
 * @param {string} platform - 平台标识
 * @returns {string} 平台显示名称
 */
export function getPlatformName(platform) {
    const names = {
        tianyi: '天翼网盘',
        baidu: '百度网盘',
        aliyun: '阿里云盘',
        quark: '夸克网盘',
        lanzou: '蓝奏云',
        onedrive: 'OneDrive',
        googledrive: 'Google Drive'
    };
    return names[platform] || platform;
}

/**
 * 防抖函数
 * @param {Function} func - 要防抖的函数
 * @param {number} wait - 等待时间（毫秒）
 * @param {boolean} immediate - 是否立即执行
 * @returns {Function} 防抖后的函数
 */
export function debounce(func, wait, immediate = false) {
    let timeout;
    return function executedFunction(...args) {
        const later = () => {
            timeout = null;
            if (!immediate) {
                func.apply(this, args);
            }
        };
        const callNow = immediate && !timeout;
        clearTimeout(timeout);
        timeout = setTimeout(later, wait);
        if (callNow) {
            func.apply(this, args);
        }
    };
}

/**
 * 节流函数
 * @param {Function} func - 要节流的函数
 * @param {number} limit - 时间限制（毫秒）
 * @returns {Function} 节流后的函数
 */
export function throttle(func, limit) {
    let inThrottle;
    return function executedFunction(...args) {
        if (!inThrottle) {
            func.apply(this, args);
            inThrottle = true;
            setTimeout(() => (inThrottle = false), limit);
        }
    };
}

/**
 * 格式化日期
 * @param {Date|string} date - 日期对象或日期字符串
 * @param {string} format - 格式字符串，默认为'YYYY-MM-DD'
 * @returns {string} 格式化后的日期字符串
 */
export function formatDate(date, format = 'YYYY-MM-DD') {
    if (!date) {
        return '';
    }

    const d = new Date(date);
    if (isNaN(d.getTime())) {
        return '';
    }

    const year = d.getFullYear();
    const month = String(d.getMonth() + 1).padStart(2, '0');
    const day = String(d.getDate()).padStart(2, '0');
    const hours = String(d.getHours()).padStart(2, '0');
    const minutes = String(d.getMinutes()).padStart(2, '0');
    const seconds = String(d.getSeconds()).padStart(2, '0');

    return format
        .replace('YYYY', year)
        .replace('MM', month)
        .replace('DD', day)
        .replace('HH', hours)
        .replace('mm', minutes)
        .replace('ss', seconds);
}

/**
 * 格式化文件大小
 * @param {number} bytes - 字节数
 * @param {number} decimals - 小数位数，默认为2
 * @returns {string} 格式化后的文件大小
 */
export function formatFileSize(bytes, decimals = 2) {
    if (bytes === 0) {
        return '0 Bytes';
    }

    const k = 1024;
    const dm = decimals < 0 ? 0 : decimals;
    const sizes = ['Bytes', 'KB', 'MB', 'GB', 'TB', 'PB', 'EB', 'ZB', 'YB'];

    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(dm)) + ' ' + sizes[i];
}

/**
 * 生成唯一ID
 * @param {string} prefix - 前缀，默认为空
 * @returns {string} 唯一ID
 */
export function generateId(prefix = '') {
    const timestamp = Date.now().toString(36);
    const random = Math.random().toString(36).substr(2, 5);
    return prefix + timestamp + random;
}

/**
 * 深拷贝对象
 * @param {any} obj - 要拷贝的对象
 * @returns {any} 拷贝后的对象
 */
export function deepClone(obj) {
    if (obj === null || typeof obj !== 'object') {
        return obj;
    }

    if (obj instanceof Date) {
        return new Date(obj.getTime());
    }

    if (obj instanceof Array) {
        return obj.map(item => deepClone(item));
    }

    if (typeof obj === 'object') {
        const clonedObj = {};
        Object.keys(obj).forEach(key => {
            clonedObj[key] = deepClone(obj[key]);
        });
        return clonedObj;
    }
}

/**
 * 检查是否为空值
 * @param {any} value - 要检查的值
 * @returns {boolean} 是否为空
 */
export function isEmpty(value) {
    if (value === null || value === undefined) {
        return true;
    }
    if (typeof value === 'string') {
        return value.trim() === '';
    }
    if (Array.isArray(value)) {
        return value.length === 0;
    }
    if (typeof value === 'object') {
        return Object.keys(value).length === 0;
    }
    return false;
}

/**
 * 安全的JSON解析
 * @param {string} jsonString - JSON字符串
 * @param {any} defaultValue - 默认值
 * @returns {any} 解析结果或默认值
 */
export function safeJsonParse(jsonString, defaultValue = null) {
    try {
        return JSON.parse(jsonString);
    } catch (error) {
        console.warn('JSON parse error:', error);
        return defaultValue;
    }
}

/**
 * 安全的JSON字符串化
 * @param {any} obj - 要字符串化的对象
 * @param {string} defaultValue - 默认值
 * @returns {string} JSON字符串或默认值
 */
export function safeJsonStringify(obj, defaultValue = '{}') {
    try {
        return JSON.stringify(obj);
    } catch (error) {
        console.warn('JSON stringify error:', error);
        return defaultValue;
    }
}

/**
 * 获取URL参数
 * @param {string} name - 参数名
 * @param {string} url - URL，默认为当前页面URL
 * @returns {string|null} 参数值
 */
export function getUrlParameter(name, url = window.location.href) {
    const urlObj = new URL(url);
    return urlObj.searchParams.get(name);
}

/**
 * 设置URL参数
 * @param {string} name - 参数名
 * @param {string} value - 参数值
 * @param {boolean} pushState - 是否更新浏览器历史，默认为false
 */
export function setUrlParameter(name, value, pushState = false) {
    const url = new URL(window.location.href);
    url.searchParams.set(name, value);

    if (pushState) {
        window.history.pushState({}, '', url.toString());
    } else {
        window.history.replaceState({}, '', url.toString());
    }
}

/**
 * 移除URL参数
 * @param {string} name - 参数名
 * @param {boolean} pushState - 是否更新浏览器历史，默认为false
 */
export function removeUrlParameter(name, pushState = false) {
    const url = new URL(window.location.href);
    url.searchParams.delete(name);

    if (pushState) {
        window.history.pushState({}, '', url.toString());
    } else {
        window.history.replaceState({}, '', url.toString());
    }
}

/**
 * 复制文本到剪贴板
 * @param {string} text - 要复制的文本
 * @returns {Promise<boolean>} 是否复制成功
 */
export async function copyToClipboard(text) {
    try {
        if (navigator.clipboard && window.isSecureContext) {
            await navigator.clipboard.writeText(text);
            return true;
        } else {
            // 降级方案
            const textArea = document.createElement('textarea');
            textArea.value = text;
            textArea.style.position = 'fixed';
            textArea.style.left = '-999999px';
            textArea.style.top = '-999999px';
            document.body.appendChild(textArea);
            textArea.focus();
            textArea.select();
            const result = document.execCommand('copy');
            document.body.removeChild(textArea);
            return result;
        }
    } catch (error) {
        console.error('Copy to clipboard failed:', error);
        return false;
    }
}

/**
 * 简单的模板字符串替换
 * @param {string} template - 模板字符串
 * @param {Object} data - 数据对象
 * @returns {string} 替换后的字符串
 */
export function template(template, data) {
    return template.replace(/\{\{(\w+)\}\}/g, (match, key) => {
        return data[key] !== undefined ? data[key] : match;
    });
}

/**
 * 搜索历史管理类
 */
export class SearchHistory {
    constructor(maxSize = 20, storageKey = 'search_history') {
        this.maxSize = maxSize;
        this.storageKey = storageKey;
        this.history = this.loadHistory();
    }

    /**
     * 添加搜索记录
     * @param {string} keyword - 搜索关键词
     */
    add(keyword) {
        if (!keyword || typeof keyword !== 'string') return;

        const trimmedKeyword = keyword.trim();
        if (!trimmedKeyword) return;

        // 移除已存在的相同记录
        this.history = this.history.filter(item => item.keyword !== trimmedKeyword);

        // 添加新记录到开头
        this.history.unshift({
            keyword: trimmedKeyword,
            timestamp: Date.now(),
            count: this.getSearchCount(trimmedKeyword) + 1
        });

        // 限制历史记录数量
        if (this.history.length > this.maxSize) {
            this.history = this.history.slice(0, this.maxSize);
        }

        this.saveHistory();
    }

    /**
     * 获取搜索历史
     * @param {number} limit - 限制返回数量
     * @returns {Array} 搜索历史数组
     */
    get(limit = null) {
        const result = [...this.history];
        return limit ? result.slice(0, limit) : result;
    }

    /**
     * 获取热门搜索
     * @param {number} limit - 限制返回数量
     * @returns {Array} 热门搜索数组
     */
    getPopular(limit = 10) {
        return [...this.history]
            .sort((a, b) => b.count - a.count)
            .slice(0, limit);
    }

    /**
     * 搜索历史记录
     * @param {string} query - 搜索查询
     * @returns {Array} 匹配的历史记录
     */
    search(query) {
        if (!query) return this.get();

        const lowerQuery = query.toLowerCase();
        return this.history.filter(item =>
            item.keyword.toLowerCase().includes(lowerQuery)
        );
    }

    /**
     * 删除指定搜索记录
     * @param {string} keyword - 要删除的关键词
     */
    remove(keyword) {
        this.history = this.history.filter(item => item.keyword !== keyword);
        this.saveHistory();
    }

    /**
     * 清空搜索历史
     */
    clear() {
        this.history = [];
        this.saveHistory();
    }

    /**
     * 获取搜索次数
     * @param {string} keyword - 搜索关键词
     * @returns {number} 搜索次数
     */
    getSearchCount(keyword) {
        const item = this.history.find(item => item.keyword === keyword);
        return item ? item.count : 0;
    }

    /**
     * 加载历史记录
     * @returns {Array} 历史记录数组
     */
    loadHistory() {
        try {
            const stored = localStorage.getItem(this.storageKey);
            return stored ? JSON.parse(stored) : [];
        } catch (error) {
            console.warn('Failed to load search history:', error);
            return [];
        }
    }

    /**
     * 保存历史记录
     */
    saveHistory() {
        try {
            localStorage.setItem(this.storageKey, JSON.stringify(this.history));
        } catch (error) {
            console.warn('Failed to save search history:', error);
        }
    }

    /**
     * 获取建议搜索词
     * @param {string} input - 当前输入
     * @param {number} limit - 限制返回数量
     * @returns {Array} 建议搜索词数组
     */
    getSuggestions(input, limit = 5) {
        if (!input) return this.get(limit);

        const lowerInput = input.toLowerCase();
        const suggestions = [];

        // 首先添加完全匹配的历史记录
        const exactMatches = this.history.filter(item =>
            item.keyword.toLowerCase().startsWith(lowerInput)
        );
        suggestions.push(...exactMatches);

        // 然后添加包含输入的历史记录
        if (suggestions.length < limit) {
            const partialMatches = this.history.filter(item =>
                item.keyword.toLowerCase().includes(lowerInput) &&
                !item.keyword.toLowerCase().startsWith(lowerInput)
            );
            suggestions.push(...partialMatches);
        }

        return suggestions.slice(0, limit);
    }
}

// 创建全局搜索历史实例
export const searchHistory = new SearchHistory();
