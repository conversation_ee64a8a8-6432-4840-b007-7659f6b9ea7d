module.exports = {
    // 测试环境
    testEnvironment: 'jsdom',

    // 模块文件扩展名
    moduleFileExtensions: ['js', 'json'],

    // 测试文件匹配模式
    testMatch: [
        '<rootDir>/tests/**/*.test.js',
        '<rootDir>/src/**/*.test.js'
    ],

    // 覆盖率收集 (暂时禁用)
    collectCoverage: false,
    collectCoverageFrom: [
        'src/**/*.js',
        '!src/**/*.test.js',
        '!src/build/**',
        '!src/worker.js' // Workers 入口文件暂时排除
    ],

    // 覆盖率报告
    coverageDirectory: 'coverage',
    coverageReporters: ['text', 'lcov', 'html'],

    // 覆盖率阈值 (初始阶段设置较低)
    coverageThreshold: {
        global: {
            branches: 20,
            functions: 20,
            lines: 20,
            statements: 20
        }
    },

    // 设置文件 (暂时禁用)
    // setupFilesAfterEnv: ['<rootDir>/tests/setup.js'],

    // 模块路径映射
    moduleNameMapper: {
        '^@/(.*)$': '<rootDir>/src/$1',
        '^@config/(.*)$': '<rootDir>/src/config/$1',
        '^@utils/(.*)$': '<rootDir>/src/scripts/utils/$1',
        '^@components/(.*)$': '<rootDir>/src/scripts/components/$1'
    },

    // 转换配置 (暂时禁用)
    // transform: {
    //     '^.+\\.js$': 'babel-jest'
    // },

    // 忽略转换的模块
    transformIgnorePatterns: [
        'node_modules/(?!(.*\\.mjs$))'
    ],

    // 清除模拟
    clearMocks: true,

    // 恢复模拟
    restoreMocks: true,

    // 测试超时
    testTimeout: 10000,

    // 详细输出
    verbose: true
};
