module.exports = {
  ci: {
    collect: {
      // 要测试的URL
      url: ['http://localhost:8787'],
      // 启动服务器的命令
      startServerCommand: 'npm run dev',
      // 等待服务器启动的时间
      startServerReadyPattern: 'Ready on',
      // 启动超时时间
      startServerReadyTimeout: 30000,
      // 收集的次数
      numberOfRuns: 3,
    },
    assert: {
      // 性能预算设置
      assertions: {
        'categories:performance': ['warn', { minScore: 0.8 }],
        'categories:accessibility': ['error', { minScore: 0.9 }],
        'categories:best-practices': ['warn', { minScore: 0.8 }],
        'categories:seo': ['warn', { minScore: 0.8 }],
        'categories:pwa': 'off', // PWA功能暂时关闭
        
        // 核心Web指标
        'first-contentful-paint': ['warn', { maxNumericValue: 2000 }],
        'largest-contentful-paint': ['warn', { maxNumericValue: 2500 }],
        'cumulative-layout-shift': ['warn', { maxNumericValue: 0.1 }],
        'total-blocking-time': ['warn', { maxNumericValue: 300 }],
        
        // 安全相关检查
        'is-on-https': 'off', // 本地开发时关闭
        'uses-http2': 'off', // 本地开发时关闭
      },
    },
    upload: {
      // 暂时不上传结果，仅本地检查
      target: 'temporary-public-storage',
    },
  },
};
