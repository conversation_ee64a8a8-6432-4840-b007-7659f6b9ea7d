# 部署配置指南

## 概述

本项目支持多环境自动化部署，使用 Cloudflare Workers 作为部署平台，通过 GitHub Actions 实现 CI/CD 流程。

## 环境配置

### 1. 开发环境 (Development)

**用途**: 本地开发和测试
**URL**: `http://localhost:8787`
**特点**:
- 启用调试模式
- 禁用缓存
- 放宽安全限制
- 详细日志输出

### 2. 测试环境 (Staging)

**用途**: 预发布测试和验证
**URL**: `https://pansou-search-staging.workers.dev`
**特点**:
- 启用部分调试
- 短期缓存
- 完整安全配置
- 监控和追踪

### 3. 生产环境 (Production)

**用途**: 正式对外服务
**URL**: `https://pansou.104078.xyz`
**特点**:
- 禁用调试模式
- 完整缓存策略
- 严格安全配置
- 完整监控和告警

## 部署方式

### 1. 本地部署

```bash
# 安装依赖
npm install

# 登录 Cloudflare
npx wrangler login

# 部署到测试环境
npm run deploy:staging

# 部署到生产环境
npm run deploy:production

# 快速部署（跳过测试）
npm run deploy:staging:quick
npm run deploy:production:quick
```

### 2. 自动化部署

#### GitHub Actions 触发条件

**测试环境**:
- 推送到 `develop` 分支
- 通过所有质量检查和测试

**生产环境**:
- 推送到 `main` 分支
- 通过所有检查（包括性能和 E2E 测试）

#### 部署流程

```mermaid
graph TD
    A[代码推送] --> B[质量检查]
    B --> C[安全扫描]
    C --> D[单元测试]
    D --> E[构建应用]
    E --> F[性能测试]
    F --> G[E2E测试]
    G --> H{分支检查}
    H -->|develop| I[部署到Staging]
    H -->|main| J[部署到Production]
    I --> K[部署验证]
    J --> L[部署验证]
    L --> M[创建Release]
```

## 配置管理

### 1. Wrangler 配置

**文件**: `wrangler.toml`

```toml
name = "pansou-search"
main = "worker.js"
compatibility_date = "2023-12-01"

[env.production]
name = "pansou-search"
routes = [
  { pattern = "pansou.104078.xyz", custom_domain = true }
]

[env.staging]
name = "pansou-search-staging"
```

### 2. 环境变量

**GitHub Secrets**:
```
CLOUDFLARE_API_TOKEN=your_api_token
CLOUDFLARE_ACCOUNT_ID=your_account_id
SNYK_TOKEN=your_snyk_token
```

**Cloudflare Workers 环境变量**:
```
NODE_ENV=production
ENVIRONMENT=production
API_BASE_URL=https://pansou.252035.xyz
```

### 3. 域名配置

**生产环境**:
- 主域名: `pansou.104078.xyz`
- Workers 域名: `pansou-search.workers.dev`

**测试环境**:
- Workers 域名: `pansou-search-staging.workers.dev`

## 部署脚本

### 1. 部署前检查

```javascript
// 检查项目
- Wrangler CLI 可用性
- Cloudflare 认证状态
- 配置文件完整性
- Worker 文件存在性

// 代码质量检查
- ESLint 代码规范
- 安全漏洞扫描
- 单元测试通过
```

### 2. 构建过程

```bash
# 安装依赖
npm ci

# 代码检查
npm run lint

# 安全扫描
npm run security:audit

# 构建应用（如果有构建脚本）
npm run build
```

### 3. 部署执行

```bash
# 部署到指定环境
npx wrangler deploy --env <environment>

# 验证部署
curl -f <deployment_url>

# 创建部署标签
git tag deploy-<timestamp>
```

## 回滚策略

### 1. 快速回滚

```bash
# 回滚到上一个版本
npx wrangler rollback --env production

# 回滚到指定版本
npx wrangler rollback --env production --version <version>
```

### 2. 蓝绿部署

```bash
# 部署到新版本
npm run deploy:production

# 验证新版本
npm run test:e2e:production

# 切换流量（通过 Cloudflare 控制台）
```

### 3. 金丝雀发布

```bash
# 部署金丝雀版本
npx wrangler deploy --env canary

# 配置流量分割（5% 到金丝雀）
# 监控指标和错误率
# 逐步增加流量或回滚
```

## 监控和告警

### 1. 部署监控

**指标**:
- 部署成功率
- 部署时间
- 回滚频率
- 错误率变化

**告警**:
- 部署失败
- 性能回归
- 错误率激增
- 可用性下降

### 2. 健康检查

```bash
# 基础健康检查
curl -f https://pansou.104078.xyz/

# API 健康检查
curl -f https://pansou.104078.xyz/api/search?kw=test

# 性能检查
npm run test:performance:production
```

## 故障排除

### 1. 常见问题

**部署失败**:
```bash
# 检查认证
npx wrangler whoami

# 检查配置
npx wrangler dev --dry-run

# 查看详细日志
npx wrangler deploy --env production --verbose
```

**性能问题**:
```bash
# 检查 Worker 大小
npx wrangler deploy --dry-run

# 分析性能
npm run test:performance

# 检查缓存配置
curl -I https://pansou.104078.xyz/
```

**域名问题**:
```bash
# 检查 DNS 配置
nslookup pansou.104078.xyz

# 检查 SSL 证书
openssl s_client -connect pansou.104078.xyz:443

# 检查路由配置
npx wrangler routes list
```

### 2. 调试技巧

**本地调试**:
```bash
# 本地运行 Worker
npx wrangler dev

# 模拟生产环境
npx wrangler dev --env production --local false
```

**远程调试**:
```bash
# 查看实时日志
npx wrangler tail

# 查看特定环境日志
npx wrangler tail --env production

# 过滤日志
npx wrangler tail --format pretty --grep "error"
```

## 最佳实践

### 1. 部署策略

- **渐进式部署**: 先部署到测试环境，验证后再部署到生产环境
- **自动化测试**: 每次部署前运行完整的测试套件
- **监控告警**: 部署后密切监控关键指标
- **快速回滚**: 准备好快速回滚机制

### 2. 安全考虑

- **密钥管理**: 使用 GitHub Secrets 管理敏感信息
- **最小权限**: 仅授予必要的部署权限
- **审计日志**: 记录所有部署操作
- **环境隔离**: 确保不同环境完全隔离

### 3. 性能优化

- **资源优化**: 最小化 Worker 代码大小
- **缓存策略**: 合理配置缓存策略
- **CDN 配置**: 利用 Cloudflare CDN 加速
- **监控指标**: 持续监控性能指标

### 4. 团队协作

- **分支策略**: 使用 Git Flow 或 GitHub Flow
- **代码审查**: 所有代码变更都需要审查
- **文档维护**: 及时更新部署文档
- **知识共享**: 团队成员都应了解部署流程
