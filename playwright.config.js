import { defineConfig, devices } from '@playwright/test';

/**
 * @see https://playwright.dev/docs/test-configuration
 */
export default defineConfig({
    // 测试目录
    testDir: './tests/e2e',
    
    // 并行运行测试
    fullyParallel: true,
    
    // 失败时不继续运行其他测试
    forbidOnly: !!process.env.CI,
    
    // CI 环境中重试失败的测试
    retries: process.env.CI ? 2 : 0,
    
    // 并行工作进程数
    workers: process.env.CI ? 1 : undefined,
    
    // 报告器配置
    reporter: [
        ['html'],
        ['json', { outputFile: 'test-results/results.json' }],
        ['junit', { outputFile: 'test-results/results.xml' }]
    ],
    
    // 全局设置
    use: {
        // 基础 URL
        baseURL: 'http://localhost:8787',
        
        // 浏览器追踪
        trace: 'on-first-retry',
        
        // 截图设置
        screenshot: 'only-on-failure',
        
        // 视频录制
        video: 'retain-on-failure',
        
        // 视口大小
        viewport: { width: 1280, height: 720 }
    },

    // 项目配置 - 不同浏览器
    projects: [
        {
            name: 'chromium',
            use: { ...devices['Desktop Chrome'] }
        },
        
        {
            name: 'firefox',
            use: { ...devices['Desktop Firefox'] }
        },
        
        {
            name: 'webkit',
            use: { ...devices['Desktop Safari'] }
        },
        
        // 移动端测试
        {
            name: 'Mobile Chrome',
            use: { ...devices['Pixel 5'] }
        },
        
        {
            name: 'Mobile Safari',
            use: { ...devices['iPhone 12'] }
        }
    ],

    // 本地开发服务器
    webServer: {
        command: 'npm run dev',
        url: 'http://localhost:8787',
        reuseExistingServer: !process.env.CI,
        timeout: 120 * 1000
    }
});
