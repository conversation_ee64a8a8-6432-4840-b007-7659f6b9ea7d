/**
 * XSS防护模块
 * 为Cloudflare Workers环境提供XSS防护功能
 */

/**
 * 危险HTML标签列表
 */
const DANGEROUS_TAGS = [
    'script', 'iframe', 'object', 'embed', 'form', 'input', 'textarea',
    'button', 'select', 'option', 'link', 'meta', 'style', 'base',
    'applet', 'bgsound', 'blink', 'body', 'frame', 'frameset',
    'head', 'html', 'ilayer', 'layer', 'plaintext', 'xml'
];

/**
 * 危险属性列表
 */
const DANGEROUS_ATTRIBUTES = [
    'onload', 'onerror', 'onclick', 'onmouseover', 'onmouseout',
    'onmousedown', 'onmouseup', 'onkeydown', 'onkeyup', 'onkeypress',
    'onfocus', 'onblur', 'onchange', 'onsubmit', 'onreset',
    'onselect', 'onunload', 'onbeforeunload', 'onresize',
    'onscroll', 'ondrag', 'ondrop', 'ondragover', 'ondragenter',
    'ondragleave', 'ondragstart', 'ondragend', 'oncontextmenu',
    'javascript', 'vbscript', 'data', 'livescript', 'mocha'
];

/**
 * 危险URL协议列表
 */
const DANGEROUS_PROTOCOLS = [
    'javascript:', 'vbscript:', 'data:', 'livescript:', 'mocha:',
    'about:', 'chrome:', 'chrome-extension:', 'file:', 'res:',
    'view-source:', 'wyciwyg:', 'ms-its:', 'mk:', 'jar:'
];

/**
 * XSS防护类
 */
export class XSSProtection {
    /**
     * 清理HTML内容
     * @param {string} html - 要清理的HTML内容
     * @param {Object} options - 清理选项
     * @returns {string} 清理后的HTML内容
     */
    static sanitizeHTML(html, options = {}) {
        if (!html || typeof html !== 'string') {
            return '';
        }

        const config = {
            allowedTags: options.allowedTags || ['p', 'br', 'strong', 'em', 'u', 'i', 'b'],
            allowedAttributes: options.allowedAttributes || ['class', 'id'],
            removeScripts: options.removeScripts !== false,
            removeEvents: options.removeEvents !== false,
            removeDataUrls: options.removeDataUrls !== false,
            ...options
        };

        let cleanHTML = html;

        // 移除危险标签
        if (config.removeScripts) {
            DANGEROUS_TAGS.forEach(tag => {
                const regex = new RegExp(`<${tag}[^>]*>.*?<\/${tag}>`, 'gis');
                cleanHTML = cleanHTML.replace(regex, '');
                
                // 移除自闭合标签
                const selfClosingRegex = new RegExp(`<${tag}[^>]*\/?>`, 'gis');
                cleanHTML = cleanHTML.replace(selfClosingRegex, '');
            });
        }

        // 移除事件处理器
        if (config.removeEvents) {
            DANGEROUS_ATTRIBUTES.forEach(attr => {
                const regex = new RegExp(`\\s${attr}\\s*=\\s*['""][^'""]*['""]*`, 'gis');
                cleanHTML = cleanHTML.replace(regex, '');
            });
        }

        // 移除危险协议
        if (config.removeDataUrls) {
            DANGEROUS_PROTOCOLS.forEach(protocol => {
                const regex = new RegExp(protocol.replace(':', '\\s*:\\s*'), 'gis');
                cleanHTML = cleanHTML.replace(regex, '');
            });
        }

        // 移除不允许的标签（如果指定了允许的标签）
        if (config.allowedTags && config.allowedTags.length > 0) {
            // 创建允许标签的正则表达式
            const allowedTagsRegex = config.allowedTags.join('|');
            const tagRegex = new RegExp(`<(?!\/?(?:${allowedTagsRegex})\\b)[^>]+>`, 'gis');
            cleanHTML = cleanHTML.replace(tagRegex, '');
        }

        // 清理属性
        if (config.allowedAttributes && config.allowedAttributes.length > 0) {
            // 这是一个简化的属性清理，实际应用中可能需要更复杂的解析
            const attrRegex = /(\w+)\s*=\s*["']([^"']*)["']/g;
            cleanHTML = cleanHTML.replace(attrRegex, (match, attrName, attrValue) => {
                if (config.allowedAttributes.includes(attrName.toLowerCase())) {
                    // 清理属性值中的危险内容
                    const cleanValue = this.sanitizeAttributeValue(attrValue);
                    return `${attrName}="${cleanValue}"`;
                }
                return '';
            });
        }

        return cleanHTML.trim();
    }

    /**
     * 清理属性值
     * @param {string} value - 属性值
     * @returns {string} 清理后的属性值
     */
    static sanitizeAttributeValue(value) {
        if (!value || typeof value !== 'string') {
            return '';
        }

        let cleanValue = value;

        // 移除危险协议
        DANGEROUS_PROTOCOLS.forEach(protocol => {
            const regex = new RegExp(protocol.replace(':', '\\s*:\\s*'), 'gis');
            cleanValue = cleanValue.replace(regex, '');
        });

        // 移除JavaScript代码
        cleanValue = cleanValue.replace(/javascript\s*:/gi, '');
        cleanValue = cleanValue.replace(/on\w+\s*=/gi, '');

        return cleanValue;
    }

    /**
     * 清理文本内容（移除HTML标签）
     * @param {string} text - 要清理的文本
     * @returns {string} 清理后的纯文本
     */
    static sanitizeText(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        // 移除所有HTML标签
        let cleanText = text.replace(/<[^>]*>/g, '');
        
        // 解码HTML实体
        cleanText = this.decodeHTMLEntities(cleanText);
        
        return cleanText.trim();
    }

    /**
     * 解码HTML实体
     * @param {string} text - 包含HTML实体的文本
     * @returns {string} 解码后的文本
     */
    static decodeHTMLEntities(text) {
        const entities = {
            '&amp;': '&',
            '&lt;': '<',
            '&gt;': '>',
            '&quot;': '"',
            '&#39;': "'",
            '&apos;': "'",
            '&nbsp;': ' '
        };

        return text.replace(/&[#\w]+;/g, (entity) => {
            return entities[entity] || entity;
        });
    }

    /**
     * 编码HTML实体
     * @param {string} text - 要编码的文本
     * @returns {string} 编码后的文本
     */
    static encodeHTMLEntities(text) {
        if (!text || typeof text !== 'string') {
            return '';
        }

        const entities = {
            '&': '&amp;',
            '<': '&lt;',
            '>': '&gt;',
            '"': '&quot;',
            "'": '&#39;'
        };

        return text.replace(/[&<>"']/g, (char) => {
            return entities[char] || char;
        });
    }

    /**
     * 验证URL是否安全
     * @param {string} url - 要验证的URL
     * @returns {boolean} 是否安全
     */
    static isUrlSafe(url) {
        if (!url || typeof url !== 'string') {
            return false;
        }

        // 检查危险协议
        for (const protocol of DANGEROUS_PROTOCOLS) {
            if (url.toLowerCase().startsWith(protocol)) {
                return false;
            }
        }

        // 检查是否包含JavaScript代码
        if (/javascript\s*:/i.test(url)) {
            return false;
        }

        // 检查是否包含HTML标签
        if (/<[^>]*>/g.test(url)) {
            return false;
        }

        return true;
    }

    /**
     * 清理搜索关键词
     * @param {string} keyword - 搜索关键词
     * @returns {string} 清理后的关键词
     */
    static sanitizeSearchKeyword(keyword) {
        if (!keyword || typeof keyword !== 'string') {
            return '';
        }

        // 移除HTML标签
        let cleanKeyword = this.sanitizeText(keyword);
        
        // 移除特殊字符（保留基本的搜索字符）
        cleanKeyword = cleanKeyword.replace(/[<>\"'&]/g, '');
        
        // 限制长度
        if (cleanKeyword.length > 100) {
            cleanKeyword = cleanKeyword.substring(0, 100);
        }

        return cleanKeyword.trim();
    }

    /**
     * 清理文件名
     * @param {string} filename - 文件名
     * @returns {string} 清理后的文件名
     */
    static sanitizeFilename(filename) {
        if (!filename || typeof filename !== 'string') {
            return '';
        }

        // 移除HTML标签
        let cleanFilename = this.sanitizeText(filename);
        
        // 移除危险字符
        cleanFilename = cleanFilename.replace(/[<>:"/\\|?*]/g, '');
        
        return cleanFilename.trim();
    }

    /**
     * 检测是否包含XSS攻击
     * @param {string} input - 输入内容
     * @returns {boolean} 是否包含XSS攻击
     */
    static detectXSS(input) {
        if (!input || typeof input !== 'string') {
            return false;
        }

        const xssPatterns = [
            /<script[^>]*>.*?<\/script>/gis,
            /<iframe[^>]*>.*?<\/iframe>/gis,
            /javascript\s*:/gi,
            /on\w+\s*=/gi,
            /<img[^>]*src\s*=\s*["']javascript:/gi,
            /data\s*:\s*text\/html/gi,
            /<object[^>]*>.*?<\/object>/gis,
            /<embed[^>]*>/gis
        ];

        return xssPatterns.some(pattern => pattern.test(input));
    }
}

// 导出便捷方法
export const sanitizeHTML = XSSProtection.sanitizeHTML.bind(XSSProtection);
export const sanitizeText = XSSProtection.sanitizeText.bind(XSSProtection);
export const sanitizeSearchKeyword = XSSProtection.sanitizeSearchKeyword.bind(XSSProtection);
export const sanitizeFilename = XSSProtection.sanitizeFilename.bind(XSSProtection);
export const isUrlSafe = XSSProtection.isUrlSafe.bind(XSSProtection);
export const detectXSS = XSSProtection.detectXSS.bind(XSSProtection);
