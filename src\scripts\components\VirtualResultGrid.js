import { VirtualScroll } from './VirtualScroll.js';
import { getPlatformName, formatFileSize, formatDate } from '../utils/helpers.js';

/**
 * 虚拟搜索结果网格组件
 * 继承自 VirtualScroll，专门用于渲染搜索结果
 */
export class VirtualResultGrid extends VirtualScroll {
    constructor(container, options = {}) {
        const defaultOptions = {
            itemHeight: 140, // 搜索结果项的高度
            bufferSize: 3,
            ...options
        };
        
        super(container, defaultOptions);
        
        this.onItemClick = options.onItemClick || null;
        this.currentFilter = null;
        this.originalItems = [];
    }

    renderItem(item, index) {
        const element = document.createElement('div');
        element.className = 'result-item virtual-result-item';
        element.dataset.index = index;
        element.dataset.platform = item.platform || '';

        // 构建HTML内容
        element.innerHTML = this.buildItemHTML(item);

        // 绑定点击事件
        if (this.onItemClick) {
            const linkButton = element.querySelector('.result-link');
            if (linkButton) {
                linkButton.addEventListener('click', (e) => {
                    e.preventDefault();
                    this.onItemClick(item, index);
                });
            }
        }

        return element;
    }

    buildItemHTML(item) {
        const platformName = getPlatformName(item.platform);
        const fileSize = item.size ? formatFileSize(item.size) : '';
        const updateTime = item.time ? formatDate(item.time) : '';
        const extractCode = item.pwd ? `<span class="extract-code">提取码: ${item.pwd}</span>` : '';

        return `
            <div class="result-content">
                <div class="result-header">
                    <h3 class="result-title" title="${item.title || ''}">${item.title || '未知文件'}</h3>
                    <span class="result-platform">${platformName}</span>
                </div>
                <div class="result-meta">
                    ${fileSize ? `<span class="file-size">${fileSize}</span>` : ''}
                    ${updateTime ? `<span class="update-time">${updateTime}</span>` : ''}
                    ${extractCode}
                </div>
                <div class="result-actions">
                    <a href="${item.url || '#'}" 
                       class="result-link" 
                       target="_blank" 
                       rel="noopener noreferrer">
                        打开链接
                    </a>
                </div>
            </div>
        `;
    }

    setResults(results) {
        this.originalItems = results || [];
        this.applyCurrentFilter();
    }

    applyFilter(filterFn) {
        this.currentFilter = filterFn;
        this.applyCurrentFilter();
    }

    applyCurrentFilter() {
        let filteredItems = this.originalItems;
        
        if (this.currentFilter) {
            filteredItems = this.originalItems.filter(this.currentFilter);
        }
        
        this.setItems(filteredItems);
    }

    clearFilter() {
        this.currentFilter = null;
        this.setItems(this.originalItems);
    }

    onItemVisible(index) {
        // 当项目变为可见时，可以在这里添加懒加载逻辑
        const item = this.items[index];
        if (item && !item.loaded) {
            // 标记为已加载，避免重复处理
            item.loaded = true;
            
            // 这里可以添加图片懒加载或其他延迟加载逻辑
            this.loadItemAssets(item, index);
        }
    }

    loadItemAssets(item, index) {
        // 懒加载项目资源（如缩略图等）
        // 这里可以根据需要实现具体的懒加载逻辑
        
        // 示例：加载缩略图
        if (item.thumbnail && !item.thumbnailLoaded) {
            const img = new Image();
            img.onload = () => {
                item.thumbnailLoaded = true;
                // 更新对应的DOM元素
                this.updateItemThumbnail(index, item.thumbnail);
            };
            img.src = item.thumbnail;
        }
    }

    updateItemThumbnail(index, thumbnailUrl) {
        const element = this.contentContainer.querySelector(`[data-index="${index}"]`);
        if (element) {
            const thumbnailContainer = element.querySelector('.result-thumbnail');
            if (thumbnailContainer) {
                thumbnailContainer.innerHTML = `<img src="${thumbnailUrl}" alt="缩略图" />`;
            }
        }
    }

    getFilteredCount() {
        return this.items.length;
    }

    getTotalCount() {
        return this.originalItems.length;
    }

    searchInResults(keyword) {
        if (!keyword) {
            this.clearFilter();
            return;
        }

        const searchFilter = (item) => {
            const title = (item.title || '').toLowerCase();
            const platform = (item.platform || '').toLowerCase();
            const searchTerm = keyword.toLowerCase();
            
            return title.includes(searchTerm) || platform.includes(searchTerm);
        };

        this.applyFilter(searchFilter);
    }

    sortResults(sortBy, order = 'desc') {
        const sortedItems = [...this.originalItems];
        
        sortedItems.sort((a, b) => {
            let aValue, bValue;
            
            switch (sortBy) {
                case 'time':
                    aValue = new Date(a.time || 0);
                    bValue = new Date(b.time || 0);
                    break;
                case 'size':
                    aValue = a.size || 0;
                    bValue = b.size || 0;
                    break;
                case 'title':
                    aValue = (a.title || '').toLowerCase();
                    bValue = (b.title || '').toLowerCase();
                    break;
                default:
                    return 0;
            }
            
            if (aValue < bValue) return order === 'asc' ? -1 : 1;
            if (aValue > bValue) return order === 'asc' ? 1 : -1;
            return 0;
        });
        
        this.originalItems = sortedItems;
        this.applyCurrentFilter();
    }

    highlightKeyword(keyword) {
        if (!keyword) return;
        
        const elements = this.contentContainer.querySelectorAll('.result-title');
        elements.forEach(element => {
            const text = element.textContent;
            const highlightedText = text.replace(
                new RegExp(`(${keyword})`, 'gi'),
                '<mark>$1</mark>'
            );
            element.innerHTML = highlightedText;
        });
    }

    showEmptyState(message = '没有找到相关结果') {
        this.contentContainer.innerHTML = `
            <div class="empty-state">
                <div class="empty-icon">🔍</div>
                <div class="empty-message">${message}</div>
            </div>
        `;
    }

    showLoadingState() {
        this.contentContainer.innerHTML = `
            <div class="loading-state">
                <div class="loading-spinner"></div>
                <div class="loading-message">正在搜索...</div>
            </div>
        `;
    }
}
