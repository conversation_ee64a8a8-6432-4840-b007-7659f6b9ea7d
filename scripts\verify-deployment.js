#!/usr/bin/env node

/**
 * 部署验证脚本
 * 验证部署是否成功并运行冒烟测试
 */

import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);

class DeploymentVerifier {
    constructor() {
        this.environments = {
            staging: {
                name: 'staging',
                url: 'https://pansou-search-staging.workers.dev',
                timeout: 30000
            },
            production: {
                name: 'production', 
                url: 'https://pansou.104078.xyz',
                timeout: 30000
            }
        };
    }

    /**
     * 发送 HTTP 请求
     */
    async makeRequest(url, options = {}) {
        const controller = new AbortController();
        const timeoutId = setTimeout(() => controller.abort(), options.timeout || 30000);
        
        try {
            const response = await fetch(url, {
                ...options,
                signal: controller.signal
            });
            
            clearTimeout(timeoutId);
            return {
                success: true,
                status: response.status,
                headers: Object.fromEntries(response.headers.entries()),
                text: await response.text()
            };
        } catch (error) {
            clearTimeout(timeoutId);
            return {
                success: false,
                error: error.message
            };
        }
    }

    /**
     * 基础健康检查
     */
    async healthCheck(environment) {
        const envConfig = this.environments[environment];
        console.log(`🔍 执行 ${environment} 环境健康检查...`);
        
        const result = await this.makeRequest(envConfig.url, {
            timeout: envConfig.timeout
        });
        
        if (result.success && result.status === 200) {
            console.log(`✅ ${environment} 环境健康检查通过`);
            return true;
        } else {
            console.log(`❌ ${environment} 环境健康检查失败:`, result.error || `HTTP ${result.status}`);
            return false;
        }
    }

    /**
     * API 功能测试
     */
    async apiTest(environment) {
        const envConfig = this.environments[environment];
        console.log(`🧪 执行 ${environment} 环境 API 测试...`);
        
        const testCases = [
            {
                name: '搜索 API 测试',
                url: `${envConfig.url}/api/search?kw=test`,
                expectedStatus: 200,
                expectedContent: 'results'
            }
        ];
        
        let passedTests = 0;
        
        for (const testCase of testCases) {
            console.log(`  🔍 ${testCase.name}...`);
            
            const result = await this.makeRequest(testCase.url, {
                timeout: envConfig.timeout
            });
            
            if (result.success && result.status === testCase.expectedStatus) {
                if (!testCase.expectedContent || result.text.includes(testCase.expectedContent)) {
                    console.log(`    ✅ ${testCase.name} 通过`);
                    passedTests++;
                } else {
                    console.log(`    ❌ ${testCase.name} 失败: 响应内容不符合预期`);
                }
            } else {
                console.log(`    ❌ ${testCase.name} 失败:`, result.error || `HTTP ${result.status}`);
            }
        }
        
        const allPassed = passedTests === testCases.length;
        
        if (allPassed) {
            console.log(`✅ ${environment} 环境 API 测试全部通过`);
        } else {
            console.log(`❌ ${environment} 环境 API 测试: ${passedTests}/${testCases.length} 通过`);
        }
        
        return allPassed;
    }

    /**
     * 安全头部检查
     */
    async securityHeadersCheck(environment) {
        const envConfig = this.environments[environment];
        console.log(`🔒 执行 ${environment} 环境安全头部检查...`);
        
        const result = await this.makeRequest(envConfig.url, {
            method: 'HEAD',
            timeout: envConfig.timeout
        });
        
        if (!result.success) {
            console.log(`❌ ${environment} 环境安全头部检查失败:`, result.error);
            return false;
        }
        
        const requiredHeaders = [
            'content-security-policy',
            'x-content-type-options',
            'x-frame-options',
            'x-xss-protection'
        ];
        
        let passedChecks = 0;
        
        requiredHeaders.forEach(header => {
            const headerValue = result.headers[header] || result.headers[header.toLowerCase()];
            if (headerValue) {
                console.log(`  ✅ ${header}: ${headerValue}`);
                passedChecks++;
            } else {
                console.log(`  ❌ 缺少安全头部: ${header}`);
            }
        });
        
        const allPassed = passedChecks === requiredHeaders.length;
        
        if (allPassed) {
            console.log(`✅ ${environment} 环境安全头部检查通过`);
        } else {
            console.log(`❌ ${environment} 环境安全头部检查: ${passedChecks}/${requiredHeaders.length} 通过`);
        }
        
        return allPassed;
    }

    /**
     * 性能检查
     */
    async performanceCheck(environment) {
        const envConfig = this.environments[environment];
        console.log(`⚡ 执行 ${environment} 环境性能检查...`);
        
        const startTime = Date.now();
        
        const result = await this.makeRequest(envConfig.url, {
            timeout: envConfig.timeout
        });
        
        const responseTime = Date.now() - startTime;
        
        if (!result.success) {
            console.log(`❌ ${environment} 环境性能检查失败:`, result.error);
            return false;
        }
        
        const performanceThresholds = {
            responseTime: 2000, // 2秒
            contentLength: 1024 * 1024 // 1MB
        };
        
        const checks = [
            {
                name: '响应时间',
                value: responseTime,
                threshold: performanceThresholds.responseTime,
                unit: 'ms',
                passed: responseTime < performanceThresholds.responseTime
            }
        ];
        
        const contentLength = parseInt(result.headers['content-length'] || '0');
        if (contentLength > 0) {
            checks.push({
                name: '内容大小',
                value: contentLength,
                threshold: performanceThresholds.contentLength,
                unit: 'bytes',
                passed: contentLength < performanceThresholds.contentLength
            });
        }
        
        let passedChecks = 0;
        
        checks.forEach(check => {
            const icon = check.passed ? '✅' : '❌';
            console.log(`  ${icon} ${check.name}: ${check.value}${check.unit} (阈值: ${check.threshold}${check.unit})`);
            if (check.passed) passedChecks++;
        });
        
        const allPassed = passedChecks === checks.length;
        
        if (allPassed) {
            console.log(`✅ ${environment} 环境性能检查通过`);
        } else {
            console.log(`❌ ${environment} 环境性能检查: ${passedChecks}/${checks.length} 通过`);
        }
        
        return allPassed;
    }

    /**
     * CORS 配置检查
     */
    async corsCheck(environment) {
        const envConfig = this.environments[environment];
        console.log(`🌐 执行 ${environment} 环境 CORS 配置检查...`);
        
        const testOrigin = environment === 'production' 
            ? 'https://pansou.104078.xyz'
            : 'https://pansou-search-staging.workers.dev';
        
        const result = await this.makeRequest(envConfig.url, {
            method: 'OPTIONS',
            headers: {
                'Origin': testOrigin,
                'Access-Control-Request-Method': 'GET'
            },
            timeout: envConfig.timeout
        });
        
        if (!result.success) {
            console.log(`❌ ${environment} 环境 CORS 检查失败:`, result.error);
            return false;
        }
        
        const corsHeaders = [
            'access-control-allow-origin',
            'access-control-allow-methods',
            'access-control-allow-headers'
        ];
        
        let passedChecks = 0;
        
        corsHeaders.forEach(header => {
            const headerValue = result.headers[header] || result.headers[header.toLowerCase()];
            if (headerValue) {
                console.log(`  ✅ ${header}: ${headerValue}`);
                passedChecks++;
            } else {
                console.log(`  ❌ 缺少 CORS 头部: ${header}`);
            }
        });
        
        const allPassed = passedChecks === corsHeaders.length;
        
        if (allPassed) {
            console.log(`✅ ${environment} 环境 CORS 配置检查通过`);
        } else {
            console.log(`❌ ${environment} 环境 CORS 配置检查: ${passedChecks}/${corsHeaders.length} 通过`);
        }
        
        return allPassed;
    }

    /**
     * 执行完整的部署验证
     */
    async verify(environment) {
        const envConfig = this.environments[environment];
        
        if (!envConfig) {
            console.log(`❌ 未知环境: ${environment}`);
            return false;
        }
        
        console.log(`🚀 开始验证 ${environment} 环境部署\n`);
        
        const checks = [
            { name: '健康检查', fn: () => this.healthCheck(environment) },
            { name: 'API 测试', fn: () => this.apiTest(environment) },
            { name: '安全头部检查', fn: () => this.securityHeadersCheck(environment) },
            { name: '性能检查', fn: () => this.performanceCheck(environment) },
            { name: 'CORS 配置检查', fn: () => this.corsCheck(environment) }
        ];
        
        let passedChecks = 0;
        
        for (const check of checks) {
            try {
                const passed = await check.fn();
                if (passed) passedChecks++;
                console.log(''); // 空行分隔
            } catch (error) {
                console.log(`❌ ${check.name} 执行失败:`, error.message);
                console.log(''); // 空行分隔
            }
        }
        
        const allPassed = passedChecks === checks.length;
        
        console.log('📊 验证结果汇总:');
        console.log(`  通过检查: ${passedChecks}/${checks.length}`);
        console.log(`  环境: ${environment}`);
        console.log(`  URL: ${envConfig.url}`);
        
        if (allPassed) {
            console.log(`\n🎉 ${environment} 环境部署验证全部通过！`);
        } else {
            console.log(`\n⚠️ ${environment} 环境部署验证部分失败，请检查详细信息`);
        }
        
        return allPassed;
    }
}

// 命令行接口
if (import.meta.url === `file://${process.argv[1]}`) {
    const args = process.argv.slice(2);
    const environment = args[0];
    
    if (!environment) {
        console.log('使用方法: npm run verify <environment>');
        console.log('可用环境: staging, production');
        process.exit(1);
    }
    
    const verifier = new DeploymentVerifier();
    
    verifier.verify(environment)
        .then(success => {
            process.exit(success ? 0 : 1);
        })
        .catch(error => {
            console.error('❌ 验证过程中发生错误:', error);
            process.exit(1);
        });
}

export default DeploymentVerifier;
