# GitHub Actions CI/CD 配置指南

## 概述

本项目使用 GitHub Actions 实现完整的 CI/CD 流程，包括代码质量检查、安全扫描、测试、构建和部署。

## 工作流文件

### 1. 主 CI/CD 流程 (`.github/workflows/ci.yml`)

**触发条件**:
- 推送到 `main` 或 `develop` 分支
- 向 `main` 分支提交 Pull Request

**包含的作业**:
- **quality**: 代码质量检查 (ESLint, Prettier)
- **security**: 安全扫描 (npm audit, Snyk)
- **test**: 单元测试和集成测试
- **build**: 构建测试
- **performance**: 性能测试 (Lighthouse)
- **e2e**: 端到端测试 (Playwright)
- **deploy-staging**: 部署到测试环境
- **deploy-production**: 部署到生产环境

### 2. 安全扫描 (`.github/workflows/security.yml`)

**触发条件**:
- 每日定时扫描 (凌晨2点)
- 推送到主要分支
- Pull Request
- 手动触发

**包含的作业**:
- **dependency-scan**: 依赖安全扫描
- **code-scan**: 代码安全扫描 (CodeQL)
- **secret-scan**: 密钥扫描 (TruffleHog)
- **security-config**: 安全配置检查
- **security-report**: 生成安全报告

### 3. 代码质量检查 (`.github/workflows/quality.yml`)

**触发条件**:
- 推送到主要分支
- Pull Request

**包含的作业**:
- **lint-and-format**: 代码格式和规范检查
- **complexity-analysis**: 代码复杂度分析
- **coverage-analysis**: 代码覆盖率分析
- **docs-quality**: 文档质量检查
- **dependency-analysis**: 依赖分析
- **quality-report**: 生成质量报告

## 环境变量和密钥

### 必需的 GitHub Secrets

在 GitHub 仓库设置中配置以下密钥：

```
CLOUDFLARE_API_TOKEN=your_cloudflare_api_token
CLOUDFLARE_ACCOUNT_ID=your_cloudflare_account_id
SNYK_TOKEN=your_snyk_token
```

### 环境配置

- **NODE_VERSION**: Node.js 版本 (默认: 18)
- **staging**: 测试环境配置
- **production**: 生产环境配置

## 部署流程

### 测试环境部署

1. 推送到 `develop` 分支
2. 通过所有质量检查和测试
3. 自动部署到 staging 环境
4. 运行部署后验证

### 生产环境部署

1. 推送到 `main` 分支
2. 通过所有检查（包括性能和 E2E 测试）
3. 自动部署到 production 环境
4. 创建 GitHub Release

## 工作流依赖关系

```mermaid
graph TD
    A[代码推送] --> B[质量检查]
    A --> C[安全扫描]
    A --> D[单元测试]
    A --> E[构建测试]
    
    B --> F[性能测试]
    C --> F
    D --> F
    E --> F
    
    B --> G[E2E测试]
    C --> G
    D --> G
    E --> G
    
    F --> H[部署staging]
    G --> H
    
    F --> I[部署production]
    G --> I
    
    H --> J[验证部署]
    I --> J
```

## 质量门禁

### 代码质量要求

- ESLint 检查通过
- Prettier 格式检查通过
- 代码覆盖率 > 80%
- 复杂度分析通过

### 安全要求

- npm audit 无高危漏洞
- Snyk 扫描通过
- 无密钥泄露
- 安全配置验证通过

### 性能要求

- Lighthouse 性能评分 > 80
- 首屏加载时间 < 2s
- 核心 Web 指标达标

## 故障排除

### 常见问题

1. **依赖安装失败**
   - 检查 package.json 和 package-lock.json
   - 清理 npm 缓存

2. **测试失败**
   - 检查测试环境配置
   - 验证测试数据和模拟

3. **部署失败**
   - 检查 Cloudflare 凭据
   - 验证 wrangler.toml 配置

4. **安全扫描失败**
   - 更新依赖版本
   - 配置漏洞忽略规则

### 调试技巧

1. **查看详细日志**
   ```bash
   # 在 GitHub Actions 中启用调试
   ACTIONS_STEP_DEBUG=true
   ```

2. **本地复现问题**
   ```bash
   # 运行相同的命令
   npm ci
   npm run lint
   npm run test
   ```

3. **检查工作流状态**
   - 查看 Actions 页面
   - 检查各个步骤的输出
   - 下载构建产物

## 最佳实践

### 工作流优化

1. **并行执行**: 独立的作业并行运行
2. **缓存依赖**: 使用 npm 缓存加速构建
3. **条件执行**: 根据分支和事件类型执行不同作业
4. **失败容忍**: 非关键步骤允许失败

### 安全考虑

1. **最小权限**: 仅授予必要的权限
2. **密钥管理**: 使用 GitHub Secrets 存储敏感信息
3. **依赖固定**: 使用特定版本的 Actions
4. **审计日志**: 定期检查工作流执行记录

### 监控和报告

1. **状态徽章**: 在 README 中显示构建状态
2. **通知设置**: 配置失败通知
3. **报告上传**: 保存测试和扫描结果
4. **趋势分析**: 跟踪质量和性能指标

## 扩展配置

### 添加新的检查

1. 在相应的工作流文件中添加新步骤
2. 更新依赖和脚本
3. 配置必要的环境变量
4. 测试新的检查流程

### 自定义部署环境

1. 在 GitHub 中创建新环境
2. 配置环境特定的密钥
3. 更新工作流文件
4. 测试部署流程

### 集成第三方服务

1. 获取服务的 API 密钥
2. 配置 GitHub Secrets
3. 添加相应的 Actions 步骤
4. 验证集成效果
