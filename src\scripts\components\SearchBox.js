import { debounce } from '../utils/helpers.js';

/**
 * 搜索框组件
 * 负责处理用户搜索输入和搜索事件
 */
export class SearchBox {
    constructor(onSearch, options = {}) {
        this.onSearch = onSearch;
        this.options = {
            debounceDelay: 300, // 防抖延迟时间
            enableAutoSearch: true, // 是否启用自动搜索
            minSearchLength: 2, // 最小搜索长度
            ...options
        };
        this.init();
    }

    init() {
        this.setupDebouncedSearch();
        this.bindEvents();
    }

    setupDebouncedSearch() {
        // 创建防抖搜索函数
        this.debouncedSearch = debounce((keyword) => {
            if (keyword && keyword.length >= this.options.minSearchLength && this.onSearch) {
                this.onSearch(keyword);
            }
        }, this.options.debounceDelay);
    }

    bindEvents() {
        // 搜索按钮点击事件
        const searchBtn = document.getElementById('searchBtn');
        if (searchBtn) {
            searchBtn.addEventListener('click', () => this.handleSearch());
        }

        // 搜索框事件
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            // 回车事件 - 立即搜索
            searchInput.addEventListener('keypress', e => {
                if (e.key === 'Enter') {
                    this.handleSearch();
                }
            });

            // 输入事件 - 防抖搜索
            if (this.options.enableAutoSearch) {
                searchInput.addEventListener('input', e => {
                    const keyword = e.target.value.trim();
                    this.debouncedSearch(keyword);
                });
            }

            // 焦点事件
            searchInput.addEventListener('focus', () => {
                this.onFocus();
            });

            searchInput.addEventListener('blur', () => {
                this.onBlur();
            });
        }
    }

    handleSearch() {
        const keyword = this.getKeyword();
        if (keyword && this.onSearch) {
            this.onSearch(keyword);
        }
    }

    getKeyword() {
        const searchInput = document.getElementById('searchInput');
        return searchInput ? searchInput.value.trim() : '';
    }

    setKeyword(keyword) {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = keyword;
        }
    }

    focus() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.focus();
        }
    }

    clear() {
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.value = '';
        }
    }

    onFocus() {
        // 搜索框获得焦点时的处理
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.parentElement.classList.add('focused');
        }
    }

    onBlur() {
        // 搜索框失去焦点时的处理
        const searchInput = document.getElementById('searchInput');
        if (searchInput) {
            searchInput.parentElement.classList.remove('focused');
        }
    }

    /**
     * 设置搜索选项
     * @param {Object} options - 新的选项
     */
    setOptions(options) {
        this.options = { ...this.options, ...options };
        this.setupDebouncedSearch(); // 重新设置防抖函数
    }

    /**
     * 启用/禁用自动搜索
     * @param {boolean} enabled - 是否启用
     */
    setAutoSearch(enabled) {
        this.options.enableAutoSearch = enabled;
    }

    /**
     * 获取当前搜索选项
     * @returns {Object} 当前选项
     */
    getOptions() {
        return { ...this.options };
    }
}
