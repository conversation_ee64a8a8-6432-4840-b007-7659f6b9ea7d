/**
 * 环境配置管理
 * 集中管理不同环境的配置
 */

/**
 * 基础配置
 */
const baseConfig = {
    // API配置
    api: {
        timeout: 30000,
        retries: 3,
        rateLimit: {
            maxRequests: 100,
            windowMs: 60000
        }
    },
    
    // 缓存配置
    cache: {
        defaultTTL: 3600,
        maxAge: 86400
    },
    
    // 日志配置
    logging: {
        level: 'info',
        enableConsole: true,
        enableRemote: false
    },
    
    // 安全配置
    security: {
        enableCSP: true,
        enableCORS: true,
        enableRateLimit: true
    }
};

/**
 * 开发环境配置
 */
export const developmentConfig = {
    ...baseConfig,
    
    // 环境标识
    environment: 'development',
    
    // 调试模式
    debug: true,
    
    // API配置
    api: {
        ...baseConfig.api,
        baseUrl: 'http://localhost:8787',
        timeout: 10000
    },
    
    // 缓存配置（开发环境禁用缓存）
    cache: {
        ...baseConfig.cache,
        defaultTTL: 0,
        enabled: false
    },
    
    // 日志配置
    logging: {
        ...baseConfig.logging,
        level: 'debug',
        enableConsole: true,
        enableRemote: false
    },
    
    // 安全配置（开发环境放宽限制）
    security: {
        ...baseConfig.security,
        enableRateLimit: false,
        corsOrigins: ['http://localhost:3000', 'http://localhost:8787', 'http://127.0.0.1:8787']
    },
    
    // 性能配置
    performance: {
        enableMinification: false,
        enableCompression: false
    }
};

/**
 * 测试环境配置
 */
export const stagingConfig = {
    ...baseConfig,
    
    // 环境标识
    environment: 'staging',
    
    // 调试模式
    debug: true,
    
    // API配置
    api: {
        ...baseConfig.api,
        baseUrl: 'https://pansou-search-staging.workers.dev',
        timeout: 20000
    },
    
    // 缓存配置
    cache: {
        ...baseConfig.cache,
        defaultTTL: 1800, // 30分钟
        enabled: true
    },
    
    // 日志配置
    logging: {
        ...baseConfig.logging,
        level: 'info',
        enableConsole: true,
        enableRemote: true
    },
    
    // 安全配置
    security: {
        ...baseConfig.security,
        enableRateLimit: true,
        corsOrigins: [
            'https://pansou-search-staging.workers.dev',
            'https://staging.pansou.104078.xyz'
        ]
    },
    
    // 性能配置
    performance: {
        enableMinification: true,
        enableCompression: true
    },
    
    // 监控配置
    monitoring: {
        enableMetrics: true,
        enableTracing: true,
        sampleRate: 0.1
    }
};

/**
 * 生产环境配置
 */
export const productionConfig = {
    ...baseConfig,
    
    // 环境标识
    environment: 'production',
    
    // 调试模式
    debug: false,
    
    // API配置
    api: {
        ...baseConfig.api,
        baseUrl: 'https://pansou.104078.xyz',
        timeout: 30000,
        retries: 5
    },
    
    // 缓存配置
    cache: {
        ...baseConfig.cache,
        defaultTTL: 3600, // 1小时
        maxAge: 86400,    // 24小时
        enabled: true
    },
    
    // 日志配置
    logging: {
        ...baseConfig.logging,
        level: 'warn',
        enableConsole: false,
        enableRemote: true
    },
    
    // 安全配置
    security: {
        ...baseConfig.security,
        enableRateLimit: true,
        corsOrigins: [
            'https://pansou.104078.xyz',
            'https://pansou-search.workers.dev'
        ]
    },
    
    // 性能配置
    performance: {
        enableMinification: true,
        enableCompression: true,
        enableCDN: true
    },
    
    // 监控配置
    monitoring: {
        enableMetrics: true,
        enableTracing: true,
        enableAlerting: true,
        sampleRate: 0.01
    },
    
    // 错误处理
    errorHandling: {
        enableErrorReporting: true,
        enableStackTrace: false
    }
};

/**
 * 获取当前环境配置
 * @param {string} environment - 环境名称
 * @returns {Object} 环境配置
 */
export function getEnvironmentConfig(environment) {
    switch (environment) {
        case 'development':
            return developmentConfig;
        case 'staging':
            return stagingConfig;
        case 'production':
            return productionConfig;
        default:
            console.warn(`Unknown environment: ${environment}, using development config`);
            return developmentConfig;
    }
}

/**
 * 获取当前运行环境的配置
 * @returns {Object} 当前环境配置
 */
export function getCurrentConfig() {
    const environment = process.env.NODE_ENV || 
                       process.env.ENVIRONMENT || 
                       globalThis.ENVIRONMENT || 
                       'development';
    
    return getEnvironmentConfig(environment);
}

/**
 * 验证环境配置
 * @param {Object} config - 配置对象
 * @returns {boolean} 配置是否有效
 */
export function validateConfig(config) {
    const requiredFields = [
        'environment',
        'api.baseUrl',
        'security.corsOrigins'
    ];
    
    for (const field of requiredFields) {
        const keys = field.split('.');
        let value = config;
        
        for (const key of keys) {
            value = value?.[key];
        }
        
        if (value === undefined || value === null) {
            console.error(`Missing required config field: ${field}`);
            return false;
        }
    }
    
    return true;
}

/**
 * 合并配置
 * @param {Object} baseConfig - 基础配置
 * @param {Object} overrideConfig - 覆盖配置
 * @returns {Object} 合并后的配置
 */
export function mergeConfig(baseConfig, overrideConfig) {
    const merged = { ...baseConfig };
    
    for (const [key, value] of Object.entries(overrideConfig)) {
        if (typeof value === 'object' && value !== null && !Array.isArray(value)) {
            merged[key] = mergeConfig(merged[key] || {}, value);
        } else {
            merged[key] = value;
        }
    }
    
    return merged;
}

// 导出环境常量
export const ENVIRONMENTS = {
    DEVELOPMENT: 'development',
    STAGING: 'staging',
    PRODUCTION: 'production'
};

// 默认导出当前配置
export default getCurrentConfig();
