/**
 * 安全配置模块
 * 集中管理所有安全相关的配置
 */

/**
 * CORS配置
 */
export const CORS_CONFIG = {
    // 允许的源域名
    ALLOWED_ORIGINS: [
        'https://pansou.104078.xyz',           // 生产环境
        'https://pansou-search.workers.dev',   // Workers默认域名
        'https://pansou-search-staging.workers.dev', // 测试环境
        'http://localhost:8787',               // 本地开发
        'http://127.0.0.1:8787',              // 本地开发
    ],

    // 允许的HTTP方法
    ALLOWED_METHODS: ['GET', 'POST', 'OPTIONS'],

    // 允许的请求头
    ALLOWED_HEADERS: [
        'Content-Type',
        'Authorization',
        'X-Requested-With',
        'Accept',
        'Origin'
    ],

    // 预检请求缓存时间（秒）
    MAX_AGE: 86400, // 24小时

    // 是否允许携带凭据
    ALLOW_CREDENTIALS: false,

    // 暴露的响应头
    EXPOSED_HEADERS: ['X-Total-Count', 'X-Page-Count']
};

/**
 * Content Security Policy配置
 */
export const CSP_CONFIG = {
    // 默认源
    'default-src': ["'self'"],

    // 脚本源
    'script-src': [
        "'self'",
        "'unsafe-inline'", // 暂时允许内联脚本，后续会移除
        'https://cdnjs.cloudflare.com',
        'https://cdn.jsdelivr.net'
    ],

    // 样式源
    'style-src': [
        "'self'",
        "'unsafe-inline'", // 允许内联样式
        'https://fonts.googleapis.com',
        'https://cdnjs.cloudflare.com'
    ],

    // 图片源
    'img-src': [
        "'self'",
        'data:',
        'https:',
        'blob:'
    ],

    // 字体源
    'font-src': [
        "'self'",
        'https://fonts.gstatic.com',
        'https://cdnjs.cloudflare.com'
    ],

    // 连接源（AJAX、WebSocket等）
    'connect-src': [
        "'self'",
        'https://pansou.252035.xyz', // 搜索API
        'https://api.github.com'     // 可能的API调用
    ],

    // 媒体源
    'media-src': ["'self'"],

    // 对象源
    'object-src': ["'none'"],

    // 基础URI
    'base-uri': ["'self'"],

    // 表单提交目标
    'form-action': ["'self'"],

    // 框架祖先
    'frame-ancestors': ["'none'"],

    // 升级不安全请求
    'upgrade-insecure-requests': true,

    // 阻止混合内容
    'block-all-mixed-content': true
};

/**
 * 安全头部配置
 */
export const SECURITY_HEADERS = {
    // 防止MIME类型嗅探
    'X-Content-Type-Options': 'nosniff',

    // XSS保护
    'X-XSS-Protection': '1; mode=block',

    // 防止点击劫持
    'X-Frame-Options': 'DENY',

    // 引用策略
    'Referrer-Policy': 'strict-origin-when-cross-origin',

    // 权限策略
    'Permissions-Policy': 'geolocation=(), microphone=(), camera=()',

    // 严格传输安全（仅HTTPS）
    'Strict-Transport-Security': 'max-age=31536000; includeSubDomains; preload'
};

/**
 * 输入验证配置
 */
export const INPUT_VALIDATION_CONFIG = {
    // 最大请求体大小（字节）
    MAX_REQUEST_SIZE: 1024 * 1024, // 1MB

    // 最大URL长度
    MAX_URL_LENGTH: 2048,

    // 最大头部数量
    MAX_HEADERS_COUNT: 50,

    // 最大头部值长度
    MAX_HEADER_VALUE_LENGTH: 8192,

    // 危险字符模式
    DANGEROUS_PATTERNS: [
        /<script[^>]*>.*?<\/script>/gi,
        /<iframe[^>]*>.*?<\/iframe>/gi,
        /<object[^>]*>.*?<\/object>/gi,
        /<embed[^>]*>/gi,
        /javascript:/gi,
        /on\w+\s*=/gi,
        /data:text\/html/gi
    ],

    // SQL注入模式
    SQL_INJECTION_PATTERNS: [
        /(\b(SELECT|INSERT|UPDATE|DELETE|DROP|CREATE|ALTER|EXEC|UNION)\b)/gi,
        /(--|\/\*|\*\/|;)/g,
        /(\b(OR|AND)\b.*=.*)/gi
    ]
};

/**
 * 速率限制配置
 */
export const RATE_LIMIT_CONFIG = {
    // 每分钟最大请求数
    MAX_REQUESTS_PER_MINUTE: 60,

    // 每小时最大请求数
    MAX_REQUESTS_PER_HOUR: 1000,

    // 每天最大请求数
    MAX_REQUESTS_PER_DAY: 10000,

    // 阻止时间（秒）
    BLOCK_DURATION: 300, // 5分钟

    // 白名单IP
    WHITELIST_IPS: [
        '127.0.0.1',
        '::1'
    ]
};

/**
 * 生成CSP头部字符串
 * @param {string} environment - 环境
 * @returns {string} CSP头部值
 */
export function generateCSPHeader(environment = 'production') {
    const directives = [];

    // 复制配置以避免修改原始配置
    const config = { ...CSP_CONFIG };

    // 在开发环境中放宽一些限制
    if (environment === 'development') {
        // 开发环境允许eval（用于开发工具）
        config['script-src'] = [...config['script-src'], "'unsafe-eval'"];
    }

    for (const [directive, sources] of Object.entries(config)) {
        if (typeof sources === 'boolean') {
            if (sources) {
                directives.push(directive);
            }
        } else if (Array.isArray(sources)) {
            directives.push(`${directive} ${sources.join(' ')}`);
        }
    }

    return directives.join('; ');
}

/**
 * 检查源是否被允许
 * @param {string} origin - 请求源
 * @param {string} environment - 环境（development, staging, production）
 * @returns {boolean} 是否允许
 */
export function isOriginAllowed(origin, environment = 'production') {
    if (!origin) {
        return false;
    }

    // 开发环境允许所有localhost和127.0.0.1
    if (environment === 'development') {
        if (origin.includes('localhost') || origin.includes('127.0.0.1')) {
            return true;
        }
    }

    return CORS_CONFIG.ALLOWED_ORIGINS.includes(origin);
}

/**
 * 获取CORS头部
 * @param {string} origin - 请求源
 * @param {string} environment - 环境
 * @returns {Object} CORS头部对象
 */
export function getCORSHeaders(origin, environment = 'production') {
    const headers = {};

    // 设置允许的源
    if (isOriginAllowed(origin, environment)) {
        headers['Access-Control-Allow-Origin'] = origin;
    } else {
        // 如果源不被允许，不设置CORS头部
        return {};
    }

    // 设置其他CORS头部
    headers['Access-Control-Allow-Methods'] = CORS_CONFIG.ALLOWED_METHODS.join(', ');
    headers['Access-Control-Allow-Headers'] = CORS_CONFIG.ALLOWED_HEADERS.join(', ');
    headers['Access-Control-Max-Age'] = CORS_CONFIG.MAX_AGE.toString();

    if (CORS_CONFIG.ALLOW_CREDENTIALS) {
        headers['Access-Control-Allow-Credentials'] = 'true';
    }

    if (CORS_CONFIG.EXPOSED_HEADERS.length > 0) {
        headers['Access-Control-Expose-Headers'] = CORS_CONFIG.EXPOSED_HEADERS.join(', ');
    }

    return headers;
}

/**
 * 获取所有安全头部
 * @param {string} origin - 请求源
 * @param {string} environment - 环境
 * @returns {Object} 安全头部对象
 */
export function getSecurityHeaders(origin, environment = 'production') {
    const headers = {
        ...SECURITY_HEADERS,
        'Content-Security-Policy': generateCSPHeader(environment),
        ...getCORSHeaders(origin, environment)
    };

    // 在非HTTPS环境下移除HSTS头部
    if (environment === 'development') {
        delete headers['Strict-Transport-Security'];
    }

    return headers;
}
