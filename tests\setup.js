// 全局测试设置
// 暂时注释掉 jest-dom 以解决模块导入问题
// require('@testing-library/jest-dom');

// 模拟 Cloudflare Workers 环境
global.fetch = jest.fn();
global.Request = jest.fn();
global.Response = jest.fn();
global.URL = jest.fn();
global.URLSearchParams = jest.fn();

// 模拟浏览器 API
global.IntersectionObserver = jest.fn(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
}));

global.ResizeObserver = jest.fn(() => ({
    observe: jest.fn(),
    unobserve: jest.fn(),
    disconnect: jest.fn()
}));

// 模拟 localStorage
const localStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.localStorage = localStorageMock;

// 模拟 sessionStorage
const sessionStorageMock = {
    getItem: jest.fn(),
    setItem: jest.fn(),
    removeItem: jest.fn(),
    clear: jest.fn()
};
global.sessionStorage = sessionStorageMock;

// 模拟 console 方法（在测试中静默）
global.console = {
    ...console,
    log: jest.fn(),
    warn: jest.fn(),
    error: jest.fn(),
    info: jest.fn(),
    debug: jest.fn()
};

// 设置默认的测试超时
jest.setTimeout(10000);

// 每个测试前的清理
beforeEach(() => {
    // 清理所有模拟
    jest.clearAllMocks();

    // 重置 localStorage
    localStorageMock.getItem.mockClear();
    localStorageMock.setItem.mockClear();
    localStorageMock.removeItem.mockClear();
    localStorageMock.clear.mockClear();

    // 重置 sessionStorage
    sessionStorageMock.getItem.mockClear();
    sessionStorageMock.setItem.mockClear();
    sessionStorageMock.removeItem.mockClear();
    sessionStorageMock.clear.mockClear();

    // 清理 DOM
    document.body.innerHTML = '';
});

// 每个测试后的清理
afterEach(() => {
    // 清理定时器
    jest.clearAllTimers();
});

// 全局错误处理
process.on('unhandledRejection', (reason, promise) => {
    console.error('Unhandled Rejection at:', promise, 'reason:', reason);
});
