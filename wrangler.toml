# Cloudflare Workers 配置文件
# 网盘搜索应用 - pansou-search

name = "pansou-search"
main = "dist/worker.js"
compatibility_date = "2024-07-01"

# 资源限制配置
[limits]
cpu_ms = 50

# 开发环境配置
[env.development]
name = "pansou-search-dev"
compatibility_date = "2024-07-01"

# 测试环境配置
[env.staging]
name = "pansou-search-staging"
compatibility_date = "2024-07-01"

# 生产环境配置
[env.production]
name = "pansou-search"
compatibility_date = "2024-07-01"
routes = [
  { pattern = "pansou.104078.xyz", custom_domain = true }
]

# 构建配置
[build]
command = "npm run build"
cwd = "."
watch_dir = "src"

# 环境变量（示例，实际值在Cloudflare Dashboard中设置）
[vars]
NODE_ENV = "development"
API_BASE_URL = "https://pansou.252035.xyz"

[env.staging.vars]
NODE_ENV = "staging"
API_BASE_URL = "https://pansou.252035.xyz"

[env.production.vars]
NODE_ENV = "production"
API_BASE_URL = "https://pansou.252035.xyz"
