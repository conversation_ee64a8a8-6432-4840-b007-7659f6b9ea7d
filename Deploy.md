# 部署指南 - 网盘搜索应用

本文档提供了详细的部署步骤和配置说明，帮助您快速部署网盘搜索应用到Cloudflare Workers。

## 📋 前置要求

### 1. 系统要求

- **Node.js**: 版本 18.0.0 或更高
- **npm**: 版本 8.0.0 或更高
- **Git**: 用于版本控制
- **Cloudflare账户**: 免费账户即可

### 2. 工具安装

```bash
# 检查Node.js版本
node --version

# 检查npm版本
npm --version

# 全局安装Wrangler CLI
npm install -g wrangler

# 验证Wrangler安装
wrangler --version
```

## 🚀 快速开始

### 1. 克隆项目

```bash
git clone <your-repository-url>
cd pansou-search
```

### 2. 安装依赖

```bash
npm install
```

### 3. Cloudflare认证

```bash
# 登录Cloudflare账户
wrangler login

# 验证登录状态
wrangler whoami
```

## 🔧 环境配置

### 1. 开发环境设置

```bash
# 启动本地开发服务器
npm run dev

# 或者使用Wrangler直接启动
wrangler dev
```

访问 `http://localhost:8787` 查看应用。

### 2. 构建项目

```bash
# 开发构建
npm run build:dev

# 生产构建
npm run build

# 清理构建文件
npm run clean
```

### 3. 环境变量配置

在Cloudflare Dashboard中设置以下环境变量：

**开发环境变量：**

- `NODE_ENV`: `development`
- `API_BASE_URL`: `https://pansou.252035.xyz`

**生产环境变量：**

- `NODE_ENV`: `production`
- `API_BASE_URL`: `https://pansou.252035.xyz`

## 📦 部署步骤

### 1. 测试环境部署

```bash
# 部署到staging环境
npm run deploy:staging

# 或使用Wrangler命令
wrangler deploy --env staging
```

### 2. 生产环境部署

```bash
# 部署到production环境
npm run deploy:production

# 或使用Wrangler命令
wrangler deploy --env production
```

### 3. 快速部署（跳过测试）

```bash
# 快速部署到staging
npm run deploy:staging:quick

# 快速部署到production
npm run deploy:production:quick
```

## 🔍 部署验证

### 1. 基础健康检查

```bash
# 检查staging环境
curl -f https://pansou-search-staging.workers.dev/

# 检查production环境
curl -f https://pansou.104078.xyz/
```

### 2. API功能测试

```bash
# 测试搜索API
curl "https://pansou.104078.xyz/api/search?kw=test"
```

### 3. 使用验证脚本

```bash
# 验证staging部署
npm run verify:staging

# 验证production部署
npm run verify:production
```

## 🌐 域名配置

### 1. 自定义域名设置

1. 在Cloudflare Dashboard中添加域名
2. 配置DNS记录指向Workers
3. 更新`wrangler.toml`中的routes配置

### 2. SSL证书

Cloudflare会自动为您的域名提供SSL证书。

## 🛠️ 常见问题排查

### 1. 部署失败

```bash
# 检查认证状态
wrangler whoami

# 检查配置文件
wrangler dev --dry-run

# 查看详细部署日志
wrangler deploy --env production --verbose
```

### 2. 构建错误

```bash
# 清理并重新安装依赖
npm run clean
rm -rf node_modules package-lock.json
npm install

# 重新构建
npm run build
```

### 3. 运行时错误

```bash
# 查看实时日志
wrangler tail

# 查看特定环境日志
wrangler tail --env production

# 过滤错误日志
wrangler tail --format pretty --grep "error"
```

### 4. 性能问题

```bash
# 检查Worker大小
wrangler deploy --dry-run

# 运行性能测试
npm run test:performance
```

## 📊 监控和维护

### 1. 日志监控

```bash
# 实时监控生产环境
wrangler tail --env production

# 监控特定错误
wrangler tail --grep "ERROR"
```

### 2. 性能监控

- 使用Cloudflare Analytics查看请求统计
- 监控响应时间和错误率
- 设置告警规则

### 3. 定期维护

```bash
# 更新依赖
npm audit
npm update

# 运行安全扫描
npm run security:audit
```

## 🔄 回滚策略

### 1. 快速回滚

```bash
# 回滚到上一个版本
wrangler rollback --env production

# 回滚到指定版本
wrangler rollback --env production --version <version-id>
```

### 2. 版本管理

```bash
# 查看部署历史
wrangler deployments list

# 查看特定部署详情
wrangler deployments view <deployment-id>
```

## 📝 最佳实践

### 1. 部署流程

1. 在本地充分测试
2. 先部署到staging环境
3. 验证staging环境功能
4. 部署到production环境
5. 监控部署后的性能

### 2. 安全考虑

- 定期更新依赖包
- 使用环境变量管理敏感信息
- 启用适当的安全头
- 定期进行安全审计

### 3. 性能优化

- 最小化Worker代码大小
- 合理使用缓存策略
- 监控和优化响应时间
- 使用CDN加速静态资源

## � 高级配置

### 1. 自定义构建配置

```javascript
// webpack.config.js 自定义配置
module.exports = {
    // 自定义构建选项
    optimization: {
        minimize: true,
        usedExports: true
    }
};
```

### 2. 环境特定配置

```toml
# wrangler.toml 环境配置示例
[env.staging]
name = "pansou-search-staging"
vars = { DEBUG = "true" }

[env.production]
name = "pansou-search"
vars = { DEBUG = "false" }
```

### 3. KV存储配置（如需要）

```toml
# 添加KV存储绑定
[[kv_namespaces]]
binding = "CACHE"
id = "your-kv-namespace-id"
preview_id = "your-preview-kv-namespace-id"
```

## 🚨 故障排除指南

### 错误代码对照表

| 错误代码 | 描述         | 解决方案                  |
| -------- | ------------ | ------------------------- |
| 1001     | 认证失败     | 重新运行 `wrangler login` |
| 1002     | 配置文件错误 | 检查 `wrangler.toml` 语法 |
| 1003     | 构建失败     | 检查构建脚本和依赖        |
| 1004     | 部署超时     | 检查网络连接，重试部署    |
| 1005     | 域名配置错误 | 验证DNS设置和路由配置     |

### 常见错误解决方案

**错误: "Worker exceeded CPU time limit"**

```bash
# 解决方案：优化代码性能
npm run build -- --analyze
# 检查bundle大小和性能瓶颈
```

**错误: "Module not found"**

```bash
# 解决方案：检查依赖和构建配置
npm install
npm run build
```

**错误: "Invalid route pattern"**

```toml
# 正确的路由配置格式
routes = [
  { pattern = "example.com/*", zone_name = "example.com" }
]
```

## 📈 性能优化建议

### 1. 代码优化

- 使用Tree Shaking减少bundle大小
- 避免不必要的依赖
- 使用异步操作优化响应时间

### 2. 缓存策略

```javascript
// 示例：设置缓存头
const response = new Response(data, {
    headers: {
        'Cache-Control': 'public, max-age=3600',
        'Content-Type': 'application/json'
    }
});
```

### 3. 监控指标

- 响应时间 < 100ms
- 错误率 < 0.1%
- CPU使用率 < 50ms
- 内存使用 < 128MB

## 🔐 安全配置

### 1. 安全头设置

```javascript
// 添加安全响应头
const securityHeaders = {
    'X-Content-Type-Options': 'nosniff',
    'X-Frame-Options': 'DENY',
    'X-XSS-Protection': '1; mode=block',
    'Strict-Transport-Security': 'max-age=31536000'
};
```

### 2. CORS配置

```javascript
// CORS设置示例
const corsHeaders = {
    'Access-Control-Allow-Origin': '*',
    'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
    'Access-Control-Allow-Headers': 'Content-Type'
};
```

## �📞 支持和帮助

### 官方资源

- [Cloudflare Workers文档](https://developers.cloudflare.com/workers/)
- [Wrangler CLI文档](https://developers.cloudflare.com/workers/wrangler/)
- [Cloudflare社区论坛](https://community.cloudflare.com/)

### 项目资源

- 项目GitHub仓库
- 技术文档wiki
- 问题反馈Issues

### 紧急联系

如果遇到生产环境紧急问题：

1. 立即执行回滚操作
2. 查看错误日志定位问题
3. 联系技术支持团队

---

**重要提醒**:

- 生产环境部署前务必在staging环境充分测试
- 定期备份重要配置和数据
- 保持依赖包的及时更新
- 监控应用性能和安全状态
