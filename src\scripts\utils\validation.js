import { z } from 'zod';
import { ERROR_MESSAGES, SEARCH_CONFIG, PAGINATION_CONFIG } from '../../config/constants.js';
import { XSSProtection, detectXSS } from './xss-protection.js';

/**
 * 自定义验证错误类
 */
export class ValidationError extends Error {
    constructor(message, field = null, code = 'VALIDATION_ERROR') {
        super(message);
        this.name = 'ValidationError';
        this.field = field;
        this.code = code;
        this.timestamp = new Date().toISOString();
    }
}

/**
 * 搜索关键词验证schema
 */
const searchKeywordSchema = z
    .string()
    .trim()
    .min(SEARCH_CONFIG.MIN_KEYWORD_LENGTH, ERROR_MESSAGES.KEYWORD_TOO_SHORT)
    .max(SEARCH_CONFIG.MAX_KEYWORD_LENGTH, ERROR_MESSAGES.KEYWORD_TOO_LONG)
    .refine(
        (value) => value.length > 0,
        { message: ERROR_MESSAGES.INVALID_KEYWORD }
    )
    .refine(
        (value) => !detectXSS(value),
        { message: '搜索关键词包含潜在的安全威胁' }
    )
    .refine(
        (value) => !/[<>\"'&]/.test(value),
        { message: '搜索关键词包含非法字符' }
    )
    .refine(
        (value) => !/^\s*$/.test(value),
        { message: ERROR_MESSAGES.INVALID_KEYWORD }
    );

/**
 * 页码验证schema
 */
const pageSchema = z
    .number()
    .int('页码必须是整数')
    .min(1, '页码必须大于0')
    .max(10000, '页码不能超过10000');

/**
 * 每页大小验证schema
 */
const pageSizeSchema = z
    .number()
    .int('每页大小必须是整数')
    .min(1, '每页大小必须大于0')
    .max(100, '每页大小不能超过100')
    .refine(
        (value) => PAGINATION_CONFIG.PAGE_SIZE_OPTIONS.includes(value),
        { message: '每页大小必须是预定义的选项之一' }
    );

/**
 * URL参数验证schema
 */
const urlParamSchema = z
    .string()
    .trim()
    .max(1000, 'URL参数过长')
    .refine(
        (value) => !/[<>\"'&]/.test(value),
        { message: 'URL参数包含非法字符' }
    );

/**
 * 搜索参数验证schema
 */
const searchParamsSchema = z.object({
    kw: searchKeywordSchema,
    refresh: z.enum(['true', 'false']).optional().default('false'),
    res: z.enum(['merge', 'separate']).optional().default('merge'),
    src: z.enum(['all', 'baidu', 'aliyun', 'quark', 'tianyi']).optional().default('all'),
    plugins: urlParamSchema.optional(),
    page: pageSchema.optional().default(1),
    pageSize: pageSizeSchema.optional().default(PAGINATION_CONFIG.DEFAULT_PAGE_SIZE)
});

/**
 * 文件名验证schema
 */
const filenameSchema = z
    .string()
    .trim()
    .max(255, '文件名过长')
    .refine(
        (value) => !/[<>:"/\\|?*]/.test(value),
        { message: '文件名包含非法字符' }
    );

/**
 * URL验证schema
 */
const urlSchema = z
    .string()
    .url('无效的URL格式')
    .refine(
        (value) => {
            try {
                const url = new URL(value);
                return ['http:', 'https:'].includes(url.protocol);
            } catch {
                return false;
            }
        },
        { message: 'URL必须使用HTTP或HTTPS协议' }
    );

/**
 * 增强的输入验证器
 */
export class EnhancedValidator {
    /**
     * 验证搜索关键词
     * @param {string} keyword - 搜索关键词
     * @returns {string} 验证并清理后的关键词
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static validateSearchKeyword(keyword) {
        try {
            return searchKeywordSchema.parse(keyword);
        } catch (error) {
            if (error instanceof z.ZodError) {
                const firstError = error.errors?.[0];
                const message = firstError?.message || ERROR_MESSAGES.INVALID_KEYWORD;
                throw new ValidationError(message, 'keyword');
            }
            throw new ValidationError(ERROR_MESSAGES.INVALID_KEYWORD, 'keyword');
        }
    }

    /**
     * 验证页码
     * @param {number|string} page - 页码
     * @returns {number} 验证后的页码
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static validatePage(page) {
        try {
            const numPage = typeof page === 'string' ? parseInt(page, 10) : page;
            return pageSchema.parse(numPage);
        } catch (error) {
            if (error instanceof z.ZodError) {
                const firstError = error.errors?.[0];
                const message = firstError?.message || '无效的页码';
                throw new ValidationError(message, 'page');
            }
            throw new ValidationError('无效的页码', 'page');
        }
    }

    /**
     * 验证每页大小
     * @param {number|string} pageSize - 每页大小
     * @returns {number} 验证后的每页大小
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static validatePageSize(pageSize) {
        try {
            const numPageSize = typeof pageSize === 'string' ? parseInt(pageSize, 10) : pageSize;
            return pageSizeSchema.parse(numPageSize);
        } catch (error) {
            if (error instanceof z.ZodError) {
                const firstError = error.errors?.[0];
                const message = firstError?.message || '无效的每页大小';
                throw new ValidationError(message, 'pageSize');
            }
            throw new ValidationError('无效的每页大小', 'pageSize');
        }
    }

    /**
     * 验证搜索参数
     * @param {Object} params - 搜索参数对象
     * @returns {Object} 验证并清理后的参数
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static validateSearchParams(params) {
        try {
            return searchParamsSchema.parse(params);
        } catch (error) {
            if (error instanceof z.ZodError) {
                const firstError = error.errors?.[0];
                const message = firstError?.message || '无效的搜索参数';
                const field = firstError?.path?.join('.') || 'params';
                throw new ValidationError(message, field);
            }
            throw new ValidationError('无效的搜索参数');
        }
    }

    /**
     * 验证文件名
     * @param {string} filename - 文件名
     * @returns {string} 验证后的文件名
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static validateFilename(filename) {
        try {
            return filenameSchema.parse(filename);
        } catch (error) {
            if (error instanceof z.ZodError) {
                const firstError = error.errors?.[0];
                const message = firstError?.message || '无效的文件名';
                throw new ValidationError(message, 'filename');
            }
            throw new ValidationError('无效的文件名', 'filename');
        }
    }

    /**
     * 验证URL
     * @param {string} url - URL
     * @returns {string} 验证后的URL
     * @throws {ValidationError} 验证失败时抛出错误
     */
    static validateUrl(url) {
        try {
            return urlSchema.parse(url);
        } catch (error) {
            if (error instanceof z.ZodError) {
                const firstError = error.errors?.[0];
                const message = firstError?.message || '无效的URL';
                throw new ValidationError(message, 'url');
            }
            throw new ValidationError('无效的URL', 'url');
        }
    }

    /**
     * 清理HTML内容（使用XSS防护模块）
     * @param {string} html - HTML内容
     * @param {Object} options - 清理选项
     * @returns {string} 清理后的内容
     */
    static sanitizeHTML(html, options = {}) {
        return XSSProtection.sanitizeHTML(html, options);
    }

    /**
     * 清理文本内容
     * @param {string} text - 文本内容
     * @returns {string} 清理后的文本
     */
    static sanitizeText(text) {
        return XSSProtection.sanitizeText(text);
    }

    /**
     * 检测XSS攻击
     * @param {string} input - 输入内容
     * @returns {boolean} 是否包含XSS攻击
     */
    static detectXSS(input) {
        return XSSProtection.detectXSS(input);
    }
}

// 导出验证schemas供其他模块使用
export {
    searchKeywordSchema,
    pageSchema,
    pageSizeSchema,
    searchParamsSchema,
    filenameSchema,
    urlSchema
};
