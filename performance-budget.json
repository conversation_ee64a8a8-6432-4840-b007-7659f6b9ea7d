{"budget": [{"path": "/*", "timings": [{"metric": "first-contentful-paint", "budget": 2000, "tolerance": 200}, {"metric": "largest-contentful-paint", "budget": 2500, "tolerance": 300}, {"metric": "cumulative-layout-shift", "budget": 0.1, "tolerance": 0.02}, {"metric": "total-blocking-time", "budget": 300, "tolerance": 50}, {"metric": "speed-index", "budget": 3000, "tolerance": 500}], "resourceSizes": [{"resourceType": "script", "budget": 200, "tolerance": 50}, {"resourceType": "stylesheet", "budget": 50, "tolerance": 10}, {"resourceType": "image", "budget": 500, "tolerance": 100}, {"resourceType": "font", "budget": 100, "tolerance": 20}, {"resourceType": "total", "budget": 1000, "tolerance": 200}], "resourceCounts": [{"resourceType": "script", "budget": 10, "tolerance": 2}, {"resourceType": "stylesheet", "budget": 5, "tolerance": 1}, {"resourceType": "image", "budget": 20, "tolerance": 5}, {"resourceType": "font", "budget": 5, "tolerance": 1}, {"resourceType": "total", "budget": 50, "tolerance": 10}]}], "categories": {"performance": {"budget": 80, "tolerance": 10}, "accessibility": {"budget": 90, "tolerance": 5}, "best-practices": {"budget": 80, "tolerance": 10}, "seo": {"budget": 80, "tolerance": 10}, "pwa": {"budget": 50, "tolerance": 20}}, "thresholds": {"performance": {"error": 70, "warn": 80}, "accessibility": {"error": 80, "warn": 90}, "best-practices": {"error": 70, "warn": 80}, "seo": {"error": 70, "warn": 80}}, "settings": {"numberOfRuns": 3, "skipAudits": ["uses-http2", "is-on-https"], "onlyCategories": ["performance", "accessibility", "best-practices", "seo"]}}